using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace MCP
{
    /// <summary>
    /// Rubik's Cube Controller for managing cube creation and animation via MCP
    /// </summary>
    public class RubiksCubeController : MonoBehaviour
    {
        [Header("Cube Configuration")]
        public Vector3 cubePosition = Vector3.zero;
        public float cubeSize = 2.0f;
        public bool autoCreateOnStart = false;

        [Header("Animation Settings")]
        public float animationDuration = 3.0f;
        public string[] availableFaces = { "front", "back", "left", "right", "top", "bottom" };
        public int currentFaceIndex = 0;

        [Header("Debug")]
        public bool enableDebugLogs = true;

        // Events
        public event Action OnCubeCreated;
        public event Action OnAnimationStarted;
        public event Action OnAnimationCompleted;
        public event Action<string> OnError;

        // Private fields
        private MCPCommunicationManager mcpManager;
        private bool isCubeCreated = false;
        private bool isAnimating = false;
        private Coroutine animationCoroutine;

        void Start()
        {
            // Get MCP manager instance
            mcpManager = MCPCommunicationManager.Instance;
            if (mcpManager == null)
            {
                LogError("MCPCommunicationManager instance not found!");
                return;
            }

            // Subscribe to MCP events
            mcpManager.OnConnected += OnMCPConnected;
            mcpManager.OnDisconnected += OnMCPDisconnected;
            mcpManager.OnError += OnMCPError;

            if (autoCreateOnStart && mcpManager.IsConnected)
            {
                CreateRubiksCube();
            }
        }

        void OnDestroy()
        {
            if (mcpManager != null)
            {
                mcpManager.OnConnected -= OnMCPConnected;
                mcpManager.OnDisconnected -= OnMCPDisconnected;
                mcpManager.OnError -= OnMCPError;
            }

            if (animationCoroutine != null)
            {
                StopCoroutine(animationCoroutine);
            }
        }

        /// <summary>
        /// Create Rubik's cube in Blender
        /// </summary>
        public void CreateRubiksCube()
        {
            if (mcpManager == null || !mcpManager.IsConnected)
            {
                LogError("MCP Manager not connected!");
                OnError?.Invoke("MCP Manager not connected");
                return;
            }

            if (isCubeCreated)
            {
                LogDebug("Cube already created. Clearing scene first...");
                ClearScene();
            }

            LogDebug($"Creating Rubik's cube at position {cubePosition} with size {cubeSize}");
            mcpManager.RequestRubiksCubeCreation(cubePosition, cubeSize);
            isCubeCreated = true;
            OnCubeCreated?.Invoke();
        }

        /// <summary>
        /// Start face completion animation
        /// </summary>
        public void StartFaceAnimation(string face = null)
        {
            if (mcpManager == null || !mcpManager.IsConnected)
            {
                LogError("MCP Manager not connected!");
                OnError?.Invoke("MCP Manager not connected");
                return;
            }

            if (!isCubeCreated)
            {
                LogError("Cube not created yet!");
                OnError?.Invoke("Cube not created yet");
                return;
            }

            if (isAnimating)
            {
                LogError("Animation already in progress!");
                OnError?.Invoke("Animation already in progress");
                return;
            }

            string targetFace = face ?? availableFaces[currentFaceIndex];
            LogDebug($"Starting face animation for: {targetFace}");

            mcpManager.RequestRubiksCubeAnimation("solve_face", targetFace);
            isAnimating = true;
            OnAnimationStarted?.Invoke();

            // Start animation timeout coroutine
            animationCoroutine = StartCoroutine(AnimationTimeoutCoroutine());
        }

        /// <summary>
        /// Cycle through different faces for animation
        /// </summary>
        public void AnimateNextFace()
        {
            if (isAnimating)
            {
                LogError("Animation already in progress!");
                return;
            }

            currentFaceIndex = (currentFaceIndex + 1) % availableFaces.Length;
            StartFaceAnimation();
        }

        /// <summary>
        /// Clear the scene in Blender
        /// </summary>
        public void ClearScene()
        {
            if (mcpManager == null || !mcpManager.IsConnected)
            {
                LogError("MCP Manager not connected!");
                return;
            }

            LogDebug("Clearing Blender scene");
            mcpManager.RequestClearScene();
            isCubeCreated = false;
        }

        /// <summary>
        /// Animation timeout coroutine
        /// </summary>
        private IEnumerator AnimationTimeoutCoroutine()
        {
            yield return new WaitForSeconds(animationDuration + 2.0f); // Add 2 seconds buffer
            
            if (isAnimating)
            {
                LogDebug("Animation completed (timeout)");
                isAnimating = false;
                OnAnimationCompleted?.Invoke();
            }
        }

        /// <summary>
        /// Called when MCP connects
        /// </summary>
        private void OnMCPConnected()
        {
            LogDebug("MCP Connected - Ready to create Rubik's cube");
            
            if (autoCreateOnStart && !isCubeCreated)
            {
                CreateRubiksCube();
            }
        }

        /// <summary>
        /// Called when MCP disconnects
        /// </summary>
        private void OnMCPDisconnected()
        {
            LogDebug("MCP Disconnected");
            isCubeCreated = false;
            isAnimating = false;
        }

        /// <summary>
        /// Called when MCP error occurs
        /// </summary>
        private void OnMCPError(string error)
        {
            LogError($"MCP Error: {error}");
            OnError?.Invoke(error);
        }

        /// <summary>
        /// Get current face name
        /// </summary>
        public string GetCurrentFace()
        {
            return availableFaces[currentFaceIndex];
        }

        /// <summary>
        /// Check if cube is created
        /// </summary>
        public bool IsCubeCreated => isCubeCreated;

        /// <summary>
        /// Check if animation is running
        /// </summary>
        public bool IsAnimating => isAnimating;

        private void LogDebug(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[RubiksCube] {message}");
            }
        }

        private void LogError(string message)
        {
            Debug.LogError($"[RubiksCube] {message}");
        }

        void OnGUI()
        {
            if (enableDebugLogs)
            {
                GUI.Label(new Rect(10, 50, 300, 20), $"Cube Created: {isCubeCreated}");
                GUI.Label(new Rect(10, 70, 300, 20), $"Animating: {isAnimating}");
                GUI.Label(new Rect(10, 90, 300, 20), $"Current Face: {GetCurrentFace()}");
            }
        }
    }
}
